seaf.ta.reverse.cloud_ru.advanced.security_groups:
    sbertroika.security_groups.02d17408-d765-4ef9-90ea-f706bb3b3748:
        id: 02d17408-d765-4ef9-90ea-f706bb3b3748
        name: cassandra-dev-cluster_sg
        description: You can select protocols and ports that the inbound rule will
            apply to. If you do not select any protocols and ports, no protocols and
            ports will be opened. After a security group is created, you can add or
            modify security group rules as required.
        rules:
            -   description: cassandra
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1822be21-c581-4211-937d-50ededac31c1
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 02d17408-d765-4ef9-90ea-f706bb3b3748
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 02d17408-d765-4ef9-90ea-f706bb3b3748
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.037d16bd-2d5c-462a-bd5c-93c22a1d6362:
        id: 037d16bd-2d5c-462a-bd5c-93c22a1d6362
        name: openvas
        description: The security group is for general-purpose web servers. It allows
            inbound ICMP and other traffic on ports 22, 80, 443, and 3389. This group
            is used for remote login, ping, and hosting websites on ECSs.
        rules:
            -   description: '1111'
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8080
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 192973a5-ae6b-41ba-b324-1461a89765f8
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 037d16bd-2d5c-462a-bd5c-93c22a1d6362
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 037d16bd-2d5c-462a-bd5c-93c22a1d6362
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.0749028f-7a29-495e-9b05-14a015ca0cfa:
        id: 0749028f-7a29-495e-9b05-14a015ca0cfa
        name: website
        description: Website 80, 443
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 27dc4465-32d9-4665-a5d7-a552906c2b10
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 0749028f-7a29-495e-9b05-14a015ca0cfa
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to access websites over HTTPS
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0749028f-7a29-495e-9b05-14a015ca0cfa
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTP
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.08800e67-a9d4-42fc-9ccb-75a240ff4b53:
        id: 08800e67-a9d4-42fc-9ccb-75a240ff4b53
        name: sc_mail_sudasuda
        description: SudaSuda
        rules:
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: bdb89e4f-8e53-4a92-8743-ab9f0d2eebdc
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: bacula fd
                direction: ingress
                ethertype: IPv4
                protocol_port: 9102
                protocol: tcp
                remote_group_id: ee19366c-760e-49e7-adf8-9b3c30a6fd65
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: bdb89e4f-8e53-4a92-8743-ab9f0d2eebdc
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 27dc4465-32d9-4665-a5d7-a552906c2b10
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 25
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: imap
                direction: ingress
                ethertype: IPv4
                protocol_port: 993
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: zabbix vdc1
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: https
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: smtp
                direction: ingress
                ethertype: IPv4
                protocol_port: 465
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: pop
                direction: ingress
                ethertype: IPv4
                protocol_port: 995
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.0d4b9cd4-b8cd-46f8-847c-6d65251a8687:
        id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
        name: kasperskiy-srv
        description: kasperskiy main ports 13000, 14000
        rules:
            -   description: "\u0434\u043E\u0441\u0442\u0443\u043F\u043D\u043E\u0441\
                    \u0442\u044C \u0434\u043E \u0431\u0440\u043E\u043A\u0435\u0440\
                    \u0430"
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SUP-3344
                direction: ingress
                ethertype: IPv4
                protocol_port: 15000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: SUP-3344
                direction: ingress
                ethertype: IPv4
                protocol_port: 15000
                protocol: udp
                remote_group_id: 454b4e24-3d9a-4955-ab20-753ad053af86
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SUP-3344
                direction: ingress
                ethertype: IPv4
                protocol_port: 15000
                protocol: tcp
                remote_group_id: 6f86479f-6611-48a2-9c48-5e48076a94bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SEC-1166
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 5c18eb70-6595-44c9-a72a-119ad4ac97bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: DC-2636
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: cfcee57b-d2d9-4faf-8193-ab9d12bc87d0
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SEC-979
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: c6947f41-90a6-4902-bc16-20b33fa97a57
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SEC-979
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: c6947f41-90a6-4902-bc16-20b33fa97a57
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: https://job.sbertroika.ru/browse/SEC-889
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 878443a9-2ba4-470c-8f34-2d99c783a719
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: https://job.sbertroika.ru/browse/SEC-889
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 878443a9-2ba4-470c-8f34-2d99c783a719
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: TEST DELETE
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: TEST DELETE
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: TEST KSC CONN
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: b61d46ee-7b37-4e5a-a691-2ab64ac1a6cf
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: TEST CONN KSC
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: b61d46ee-7b37-4e5a-a691-2ab64ac1a6cf
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all inside SG
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 9003 -  8999
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: KIB
                direction: egress
                ethertype: IPv4
                protocol_port: 9003 -  8999
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: for get update from internet
                direction: egress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: for get update from internet
                direction: egress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: NTP
                direction: egress
                ethertype: IPv4
                protocol_port: 123
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_4_TCP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_4_TCP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_1_TCP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_1_UDP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_2_TCP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_2_UDP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.0e1a652b-3f43-4241-bb06-c469d6da7b61:
        id: 0e1a652b-3f43-4241-bb06-c469d6da7b61
        name: corp_dmz
        description: The security group is for general-purpose web servers and includes
            default rules that allow all inbound ICMP traffic and inbound traffic
            on ports 22, 80, 443, and 3389. The security group is used for remote
            login, ping, and hosting a website on ECSs.
        rules:
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTP
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTPS
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Windows ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 3389
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 0e1a652b-3f43-4241-bb06-c469d6da7b61
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0e1a652b-3f43-4241-bb06-c469d6da7b61
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.12bd75f7-d3ce-4c86-ba94-7c996df9f88f:
        id: 12bd75f7-d3ce-4c86-ba94-7c996df9f88f
        name: tg-bot-test
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 12bd75f7-d3ce-4c86-ba94-7c996df9f88f
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 12bd75f7-d3ce-4c86-ba94-7c996df9f88f
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.134f109d-544d-4c53-bdf3-5350b627ab7c:
        id: 134f109d-544d-4c53-bdf3-5350b627ab7c
        name: cce-production-cce-node-2m3n9
        description: The security group is created by CCE cluster cc3483c8-d202-11ed-9fc0-0255ac100088
            for the node
        rules:
            -   description: KLAB
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: c1980429-eccc-40cb-b6f3-08f3a2420472
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: for Prometheus
                direction: ingress
                ethertype: IPv4
                protocol_port: 9090
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 5c18eb70-6595-44c9-a72a-119ad4ac97bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10250
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10250
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********2/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10250
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 4789
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.135dab5f-aef8-4ebc-8897-cd10abd3cc9b:
        id: 135dab5f-aef8-4ebc-8897-cd10abd3cc9b
        name: test_dmz_sg
        description: The security group is for general-purpose web servers and includes
            default rules that allow all inbound ICMP traffic and inbound traffic
            on ports 22, 80, 443, and 3389. The security group is used for remote
            login, ping, and hosting a website on ECSs.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 135dab5f-aef8-4ebc-8897-cd10abd3cc9b
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 135dab5f-aef8-4ebc-8897-cd10abd3cc9b
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to access websites over HTTP
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTPS
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.1822be21-c581-4211-937d-50ededac31c1:
        id: 1822be21-c581-4211-937d-50ededac31c1
        name: release_db
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: troikaservice
                direction: ingress
                ethertype: IPv4
                protocol_port: 5672
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: RDS
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: RDS
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: from region64-db1.tkp2.test
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: RIVERUBUNTU
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: de1a9be0-8830-49f9-93e7-2e99842f8598
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: stageVPN
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 9004
                protocol: tcp
                remote_group_id: 454b4e24-3d9a-4955-ab20-753ad053af86
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8123
                protocol: tcp
                remote_group_id: 454b4e24-3d9a-4955-ab20-753ad053af86
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SUP-2371
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 192973a5-ae6b-41ba-b324-1461a89765f8
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: "07 \u043D\u0441\u0438"
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: "06 \u043D\u0441\u0438"
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: "10 \u043D\u0441\u0438"
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: ab1088a2-2c72-47fc-a1fb-b9dc2fefa8f7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: db enterprise
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: from metabase
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: 454b4e24-3d9a-4955-ab20-753ad053af86
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: rmq
                direction: ingress
                ethertype: IPv4
                protocol_port: 5672
                protocol: tcp
                remote_group_id: 5c18eb70-6595-44c9-a72a-119ad4ac97bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: "\u0432\u0440\u0435\u043C\u0435\u043D\u043D\u043E\u0435\
                    \ \u043F\u0440\u0430\u0432\u0438\u043B\u043E"
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 250392d6-d6f3-4fe1-a22c-bbee50f8129c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: nifi
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5672
                protocol: tcp
                remote_group_id: a22748e2-0868-46ce-b2aa-d24697b7f6b4
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: cassandra
                direction: ingress
                ethertype: IPv4
                protocol_port: 9042
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: kafka
                direction: ingress
                ethertype: IPv4
                protocol_port: 9092
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 9999
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/8
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5672
                protocol: tcp
                remote_group_id: 134f109d-544d-4c53-bdf3-5350b627ab7c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 15672
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: 2e65485e-6a67-4ba1-9467-00ffe08de2b7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/24
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: bacula fd port
                direction: ingress
                ethertype: IPv4
                protocol_port: 9102
                protocol: tcp
                remote_group_id: ee19366c-760e-49e7-adf8-9b3c30a6fd65
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Percona PMM
                direction: ingress
                ethertype: IPv4
                protocol_port: 51999 -  42000
                protocol: tcp
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Zabbix ODBC
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: airflow
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10050
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: PMA
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 1822be21-c581-4211-937d-50ededac31c1
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1822be21-c581-4211-937d-50ededac31c1
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.192973a5-ae6b-41ba-b324-1461a89765f8:
        id: 192973a5-ae6b-41ba-b324-1461a89765f8
        name: jump_sg
        description: You can select protocols and ports that the inbound rule will
            apply to. If you do not select any protocols and ports, no protocols and
            ports will be opened. After a security group is created, you can add or
            modify security group rules as required.
        rules:
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: TEST
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 037d16bd-2d5c-462a-bd5c-93c22a1d6362
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 192973a5-ae6b-41ba-b324-1461a89765f8
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 192973a5-ae6b-41ba-b324-1461a89765f8
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.1e0517ba-ee8e-4bc1-adbd-6c0c5b4f047c:
        id: 1e0517ba-ee8e-4bc1-adbd-6c0c5b4f047c
        name: telegram_sg
        description: You can select protocols and ports that the inbound rule will
            apply to. If you do not select any protocols and ports, no protocols and
            ports will be opened. After a security group is created, you can add or
            modify security group rules as required.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1e0517ba-ee8e-4bc1-adbd-6c0c5b4f047c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 1e0517ba-ee8e-4bc1-adbd-6c0c5b4f047c
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.1f449028-889b-4a82-9c12-96f4a9a6eaba:
        id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
        name: corp_sg
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: SEC-831
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SEC-831
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SEC-831
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1776
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: DC-2636
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: cfcee57b-d2d9-4faf-8193-ab9d12bc87d0
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: b61d46ee-7b37-4e5a-a691-2ab64ac1a6cf
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: logbroker
                direction: ingress
                ethertype: IPv4
                protocol_port: 4042
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: "\u0414\u043E\u0441\u0442\u0443\u043F\u043D\u043E\u0441\
                    \u0442\u044C \u0434\u043E \u0431\u0440\u043E\u043A\u0435\u0440\
                    \u0430"
                direction: ingress
                ethertype: IPv4
                protocol_port: 4042
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: "\u0414\u043E\u0441\u0442\u0443\u043F\u043D\u043E\u0441\
                    \u0442\u044C \u0434\u043E \u0431\u0440\u043E\u043A\u0435\u0440\
                    \u0430"
                direction: ingress
                ethertype: IPv4
                protocol_port: 514
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: zookeeper
                direction: ingress
                ethertype: IPv4
                protocol_port: 2181
                protocol: tcp
                remote_group_id: 2fa01d3b-0cef-4467-a3fd-66037771405d
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: https://job.sbertroika.ru/browse/DC-2197
                direction: ingress
                ethertype: IPv4
                protocol_port: 514
                protocol: tcp
                remote_group_id: 878443a9-2ba4-470c-8f34-2d99c783a719
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 20048
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 459bc5ea-7f37-4d57-b8e6-8bf1a397ee4b
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Create by sfs turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 445
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/8
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2051
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 111
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 111
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2049
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 20048
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2052
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow access to DB KSC for collect logs
                direction: egress
                ethertype: IPv4
                protocol_port: 1433
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Preview tkp2 db
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *********/32
                remote_address_group_id: null
            -   description: 389 ipa
                direction: ingress
                ethertype: IPv4
                protocol_port: 389
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: 88 ipa
                direction: ingress
                ethertype: IPv4
                protocol_port: 88
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Percona PMM
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: 1822be21-c581-4211-937d-50ededac31c1
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 389
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ************/18
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: zabbix.st.corp
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ************/18
                remote_address_group_id: null
            -   description: Used to access PostgreSQL databases
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: dd098b0b-62de-4cec-b3b9-6fafd8b9beb4
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: 1C
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.24556d98-c1b0-4f3f-b1d7-6f0593349018:
        id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
        name: freeipa
        description: Allowing traffic on all ports may introduce security risks. Exercise
            caution when selecting this option.
        rules:
            -   description: '1'
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 41bc0b0b-a9c6-46b4-8f1d-1e0490e46aae
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.2456e847-90d1-4cdf-913c-72194d7030b5:
        id: 2456e847-90d1-4cdf-913c-72194d7030b5
        name: cce-corp-new-cce-control-e1yrk
        description: Do not delete or update this security group which is created
            by CCE cluster f9df0e91-c55e-11ed-a536-0255ac100048 for the master port
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 4789
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 9443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8445
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 2456e847-90d1-4cdf-913c-72194d7030b5
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.250392d6-d6f3-4fe1-a22c-bbee50f8129c:
        id: 250392d6-d6f3-4fe1-a22c-bbee50f8129c
        name: dev_sg
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: DC-2430
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ts-gw TEMP
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: ab1088a2-2c72-47fc-a1fb-b9dc2fefa8f7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: kafka
                direction: ingress
                ethertype: IPv4
                protocol_port: 9092
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: from sdbp61
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: mysql
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5672
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Create by sfs turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 445
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2049
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 111
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2051
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 111
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2052
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 20048
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access PostgreSQL databases
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 2fa01d3b-0cef-4467-a3fd-66037771405d
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/16
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: bacula
                direction: ingress
                ethertype: IPv4
                protocol_port: 9102
                protocol: tcp
                remote_group_id: ee19366c-760e-49e7-adf8-9b3c30a6fd65
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: sfs-nas1.ru-moscow-1.hc.sbercloud.ru
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 99bfb8e9-0b41-4292-8759-be0d65dac14c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 264cdf5c-e95e-4200-bcdd-3ad7970f94b7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 250392d6-d6f3-4fe1-a22c-bbee50f8129c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 250392d6-d6f3-4fe1-a22c-bbee50f8129c
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.264cdf5c-e95e-4200-bcdd-3ad7970f94b7:
        id: 264cdf5c-e95e-4200-bcdd-3ad7970f94b7
        name: dev_vpn_sg
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: All traffic to dev_sg
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 250392d6-d6f3-4fe1-a22c-bbee50f8129c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: VPN 1194 udp
                direction: ingress
                ethertype: IPv4
                protocol_port: 1194
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 264cdf5c-e95e-4200-bcdd-3ad7970f94b7
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.27dc4465-32d9-4665-a5d7-a552906c2b10:
        id: 27dc4465-32d9-4665-a5d7-a552906c2b10
        name: ovpn
        description: vpn 1194 udp
        rules:
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: 1194
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 1194
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.28ff22db-e40d-4e48-a369-5f421ca79590:
        id: 28ff22db-e40d-4e48-a369-5f421ca79590
        name: cce-new-ticket-system-cce-node-rja97
        description: The security group is created by CCE cluster 8debf53d-921d-11ef-8c9a-0255ac10008b
            for the node
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: 59bd8e08-c7f3-4f8e-ae5b-b539996f83b8
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: 'Created by CCE,please don''t modify! Default egress
                    security group rule '
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by the master
                    node to access the worker node.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: 2cadc7b2-f244-46de-9d9a-2f5cbab7a342
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by worker nodes
                    to access each other and to access the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Traffic from the
                    source IP addresses defined in the security group must be allowed.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 28ff22db-e40d-4e48-a369-5f421ca79590
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used for the access
                    between the node and containers.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/16
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by worker nodes
                    to access each other and to access the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Allows remote access
                    to a Linux ECS using SSH.
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Default access port
                    range of the NodePort service in the cluster.
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Default access port
                    range of the NodePort service in the cluster.
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.2a6b3c80-84c2-4992-97eb-cc868aca5f82:
        id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
        name: zabbix
        description: The security group is for general-purpose web servers and includes
            default rules that allow all inbound ICMP traffic and inbound traffic
            on ports 22, 80, 443, and 3389. The security group is used for remote
            login, ping, and hosting a website on ECSs.
        rules:
            -   description: "Prometheus \u043A\u043E\u043D\u0441\u043E\u043B\u044C"
                direction: ingress
                ethertype: IPv4
                protocol_port: 9090
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/8
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: frp
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: e39386bf-ffd2-48d2-a8bc-428aede131a7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: grafana
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 1822be21-c581-4211-937d-50ededac31c1
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: mon-prox.tkp.prod
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: mail
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: bdb89e4f-8e53-4a92-8743-ab9f0d2eebdc
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/8
                remote_address_group_id: null
            -   description: "ssh \u0434\u043B\u044F admin.vpc1"
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 250392d6-d6f3-4fe1-a22c-bbee50f8129c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 406b1c71-97fa-4f49-bd18-49dfc36d2572
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 63d10ee3-d285-4be2-a001-d1b852769639
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 54acd510-91b5-405c-81f0-3ed0365feb8c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 9324a9dd-7ae1-4f1e-9791-53226c6c3cba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: fda4410a-8acc-4d19-9716-4a828c34fd38
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 69d5c1ce-ab49-4db7-8f24-e5a32e7fa9c7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 5c18eb70-6595-44c9-a72a-119ad4ac97bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 9e11583f-fa1b-42b6-8ad8-127b2f096875
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 99bfb8e9-0b41-4292-8759-be0d65dac14c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/8
                remote_address_group_id: null
            -   description: Used to access websites over HTTPS
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: Used to access websites over HTTP
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.2cadc7b2-f244-46de-9d9a-2f5cbab7a342:
        id: 2cadc7b2-f244-46de-9d9a-2f5cbab7a342
        name: cce-new-ticket-system-cce-control-rja97
        description: Do not delete or update this security group which is created
            by CCE cluster 8debf53d-921d-11ef-8c9a-0255ac10008b for the master port
        rules:
            -   description: '5432'
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: 59bd8e08-c7f3-4f8e-ae5b-b539996f83b8
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Traffic from the
                    source IP addresses defined in the security group must be allowed.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 2cadc7b2-f244-46de-9d9a-2f5cbab7a342
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by the storage
                    add-on on the worker node to access the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: 8445
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Kube-apiserver service
                    port, used to manage the lifecycle of K8s resources.
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/16
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Kube-apiserver service
                    port, used to manage the lifecycle of K8s resources.
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by the networking
                    add-on on the worker node to access the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: 9443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: 'Created by CCE,please don''t modify! Default egress
                    security group rule '
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Listening port of
                    kube-apiserver of the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: 5443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.2e65485e-6a67-4ba1-9467-00ffe08de2b7:
        id: 2e65485e-6a67-4ba1-9467-00ffe08de2b7
        name: release_app
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: 69d5c1ce-ab49-4db7-8f24-e5a32e7fa9c7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 111
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2051
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 111
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2049
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 20048
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2052
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: lk-proxy01
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 2e65485e-6a67-4ba1-9467-00ffe08de2b7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 2e65485e-6a67-4ba1-9467-00ffe08de2b7
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.2fa01d3b-0cef-4467-a3fd-66037771405d:
        id: 2fa01d3b-0cef-4467-a3fd-66037771405d
        name: cce-dev-cce-node-nhnno
        description: The security group is created by CCE cluster 200792b2-a62c-11ed-a536-0255ac100048
            for the node
        rules:
            -   description: ftp passive
                direction: ingress
                ethertype: IPv4
                protocol_port: 20203 -  20200
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/8
                remote_address_group_id: null
            -   description: FTP
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/8
                remote_address_group_id: null
            -   description: elasticksearch
                direction: ingress
                ethertype: IPv4
                protocol_port: 9200
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: elasticksearch
                direction: ingress
                ethertype: IPv4
                protocol_port: 9200
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: elasticksearch
                direction: ingress
                ethertype: IPv4
                protocol_port: 9200
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 4789
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10250
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 2fa01d3b-0cef-4467-a3fd-66037771405d
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: **********
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.35a63d49-b7c0-43fb-9a3b-759a8a197084:
        id: 35a63d49-b7c0-43fb-9a3b-759a8a197084
        name: cce-develop-cce-control-uerr5
        description: Do not delete or update this security group which is created
            by CCE cluster 72b5b69e-04ce-11f0-ba84-0255ac10008f for the master port
        rules:
            -   description: Created by CCE,please don't modify! Used by containers
                    to access each other.
                direction: ingress
                ethertype: IPv4
                protocol_port: 4789
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by the storage
                    add-on on the worker node to access the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: 8445
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by the networking
                    add-on on the worker node to access the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: 9443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Kube-apiserver service
                    port, used to manage the lifecycle of K8s resources.
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Traffic from the
                    source IP addresses defined in the security group must be allowed.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 35a63d49-b7c0-43fb-9a3b-759a8a197084
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Kube-apiserver service
                    port, used to manage the lifecycle of K8s resources.
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/16
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Listening port of
                    kube-apiserver of the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: 5443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: 'Created by CCE,please don''t modify! Default egress
                    security group rule '
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.37c5a518-3367-407b-ae1e-23653d430f7c:
        id: 37c5a518-3367-407b-ae1e-23653d430f7c
        name: db
        description: '3306'
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: 37c5a518-3367-407b-ae1e-23653d430f7c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.3c8d655b-1343-41e5-b8f0-4dbe86d44f36:
        id: 3c8d655b-1343-41e5-b8f0-4dbe86d44f36
        name: sg_waf_RSq3
        description: 09b7a1c4-7809-4524-9c02-3a55ec1de431
        rules:
            -   description: WAF3
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/24
                remote_address_group_id: null
            -   description: WAF02
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: WAF3
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *********/32
                remote_address_group_id: null
            -   description: WAF02
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: WAF02
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: JIRA
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 878443a9-2ba4-470c-8f34-2d99c783a719
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: JIRA
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 878443a9-2ba4-470c-8f34-2d99c783a719
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: WAF
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: WAF
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 9999
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 39093
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 29093
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 10290
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 3c8d655b-1343-41e5-b8f0-4dbe86d44f36
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 19092
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 3c8d655b-1343-41e5-b8f0-4dbe86d44f36
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.406b1c71-97fa-4f49-bd18-49dfc36d2572:
        id: 406b1c71-97fa-4f49-bd18-49dfc36d2572
        name: preview_sg
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: soc-db test
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: PMM
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 406b1c71-97fa-4f49-bd18-49dfc36d2572
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 406b1c71-97fa-4f49-bd18-49dfc36d2572
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 406b1c71-97fa-4f49-bd18-49dfc36d2572
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.41bc0b0b-a9c6-46b4-8f1d-1e0490e46aae:
        id: 41bc0b0b-a9c6-46b4-8f1d-1e0490e46aae
        name: usergate_sg
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: SEC-???
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: logbroker
                direction: ingress
                ethertype: IPv4
                protocol_port: 1514
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ALLOW
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: MAIL
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: VIPNET
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: VPNs
                direction: ingress
                ethertype: IPv4
                protocol_port: 19210 -  19200
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: "\u041D\u0421\u041F\u041A \u043A\u043E\u043D\u043D\u0435\
                    \u043A\u0442\u043E\u0440"
                direction: ingress
                ethertype: IPv4
                protocol_port: 4000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: DC-2720 / Allow custom ports for NGFW DNAT
                direction: ingress
                ethertype: IPv4
                protocol_port: 19999 -  19000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 5d6a543e-16ab-4cd0-8da0-e39adf532d72
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: web usergate
                direction: ingress
                ethertype: IPv4
                protocol_port: 8001
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 41bc0b0b-a9c6-46b4-8f1d-1e0490e46aae
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.454b4e24-3d9a-4955-ab20-753ad053af86:
        id: 454b4e24-3d9a-4955-ab20-753ad053af86
        name: metabase-front
        description: The security group is for general-purpose web servers and includes
            default rules that allow all inbound ICMP traffic and inbound traffic
            on ports 22, 80, 443, and 3389. The security group is used for remote
            login, ping, and hosting a website on ECSs.
        rules:
            -   description: SUP-3344
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 3000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/24
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 27dc4465-32d9-4665-a5d7-a552906c2b10
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 9102
                protocol: tcp
                remote_group_id: ee19366c-760e-49e7-adf8-9b3c30a6fd65
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: lk-proxy1
                direction: ingress
                ethertype: IPv4
                protocol_port: 3000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 454b4e24-3d9a-4955-ab20-753ad053af86
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 454b4e24-3d9a-4955-ab20-753ad053af86
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.459bc5ea-7f37-4d57-b8e6-8bf1a397ee4b:
        id: 459bc5ea-7f37-4d57-b8e6-8bf1a397ee4b
        name: release_ovpn_sg
        description: security group for openvpn release stage
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: connection from internet
                direction: ingress
                ethertype: IPv4
                protocol_port: 1194
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: access from admin.vpc1
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 459bc5ea-7f37-4d57-b8e6-8bf1a397ee4b
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 459bc5ea-7f37-4d57-b8e6-8bf1a397ee4b
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.4bf07e33-60e7-4898-906c-e71a89c22b72:
        id: 4bf07e33-60e7-4898-906c-e71a89c22b72
        name: airflow
        description: airflow
        rules:
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 454b4e24-3d9a-4955-ab20-753ad053af86
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 454b4e24-3d9a-4955-ab20-753ad053af86
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 6f86479f-6611-48a2-9c48-5e48076a94bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 6f86479f-6611-48a2-9c48-5e48076a94bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 4bf07e33-60e7-4898-906c-e71a89c22b72
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 4bf07e33-60e7-4898-906c-e71a89c22b72
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.51a98557-7c2e-4330-8808-effb6ef62153:
        id: 51a98557-7c2e-4330-8808-effb6ef62153
        name: multiqr_sg
        description: You can select protocols and ports that the inbound rule will
            apply to. If you do not select any protocols and ports, no protocols and
            ports will be opened. After a security group is created, you can add or
            modify security group rules as required.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 27dc4465-32d9-4665-a5d7-a552906c2b10
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 51a98557-7c2e-4330-8808-effb6ef62153
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 51a98557-7c2e-4330-8808-effb6ef62153
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.53d94277-5efb-402e-be87-1717439c4992:
        id: 53d94277-5efb-402e-be87-1717439c4992
        name: cce-production-cce-control-2m3n9
        description: Do not delete or update this security group which is created
            by CCE cluster cc3483c8-d202-11ed-9fc0-0255ac100088 for the master port
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 9443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8445
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 4789
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 53d94277-5efb-402e-be87-1717439c4992
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.54acd510-91b5-405c-81f0-3ed0365feb8c:
        id: 54acd510-91b5-405c-81f0-3ed0365feb8c
        name: t2_sg
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: fda4410a-8acc-4d19-9716-4a828c34fd38
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9324a9dd-7ae1-4f1e-9791-53226c6c3cba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ee656bfe-2bc1-4aa4-8088-e86e09563fa9
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 54acd510-91b5-405c-81f0-3ed0365feb8c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 54acd510-91b5-405c-81f0-3ed0365feb8c
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.590d3649-912a-42dc-994f-a2670b4aaed6:
        id: 590d3649-912a-42dc-994f-a2670b4aaed6
        name: test-waf
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.59bd8e08-c7f3-4f8e-ae5b-b539996f83b8:
        id: 59bd8e08-c7f3-4f8e-ae5b-b539996f83b8
        name: release_db_new_tiket_sg
        description: Inbound ICMP and other traffic on ports 22, 80, 443, and 3389
            are allowed. Such a security group is used for remote login, ping, and
            hosting websites on ECSs.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: 28ff22db-e40d-4e48-a369-5f421ca79590
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: b4e64cc8-aff5-4ed2-aaf9-cfdbe5498864
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 59bd8e08-c7f3-4f8e-ae5b-b539996f83b8
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 59bd8e08-c7f3-4f8e-ae5b-b539996f83b8
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.5b4e1b63-0913-4023-8069-cc57365a56dd:
        id: 5b4e1b63-0913-4023-8069-cc57365a56dd
        name: webhook_sg
        description: Inbound ICMP and other traffic on ports 22, 80, 443, and 3389
            are allowed. Such a security group is used for remote login, ping, and
            hosting websites on ECSs.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: DC-2614
                direction: ingress
                ethertype: IPv4
                protocol_port: 8123
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: '8080'
                direction: ingress
                ethertype: IPv4
                protocol_port: 8080
                protocol: tcp
                remote_group_id: 878443a9-2ba4-470c-8f34-2d99c783a719
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.5c18eb70-6595-44c9-a72a-119ad4ac97bb:
        id: 5c18eb70-6595-44c9-a72a-119ad4ac97bb
        name: release_sg
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SEC-1166
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 134f109d-544d-4c53-bdf3-5350b627ab7c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/24
                remote_address_group_id: null
            -   description: for rabbit
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********3/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5672
                protocol: tcp
                remote_group_id: 134f109d-544d-4c53-bdf3-5350b627ab7c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: bacula fd port
                direction: ingress
                ethertype: IPv4
                protocol_port: 9102
                protocol: tcp
                remote_group_id: ee19366c-760e-49e7-adf8-9b3c30a6fd65
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: c6947f41-90a6-4902-bc16-20b33fa97a57
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ee656bfe-2bc1-4aa4-8088-e86e09563fa9
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 5c18eb70-6595-44c9-a72a-119ad4ac97bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 5c18eb70-6595-44c9-a72a-119ad4ac97bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.5d6a543e-16ab-4cd0-8da0-e39adf532d72:
        id: 5d6a543e-16ab-4cd0-8da0-e39adf532d72
        name: nginx_test_sg
        description: Inbound ICMP and other traffic on ports 22, 80, 443, and 3389
            are allowed. Such a security group is used for remote login, ping, and
            hosting websites on ECSs.
        rules:
            -   description: "\u041F\u0440\u0430\u0432\u0438\u043B\u043E \u0434\u043B\
                    \u044F NGINX"
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 41bc0b0b-a9c6-46b4-8f1d-1e0490e46aae
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 5d6a543e-16ab-4cd0-8da0-e39adf532d72
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.5faf428b-91cf-491d-b843-da2b5da691db:
        id: 5faf428b-91cf-491d-b843-da2b5da691db
        name: nifi_sg
        description: The security group is for general-purpose web servers. It allows
            inbound ICMP and other traffic on ports 22, 80, 443, and 3389. This group
            is used for remote login, ping, and hosting websites on ECSs.
        rules:
            -   description: ssh
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8443
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 5faf428b-91cf-491d-b843-da2b5da691db
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 5faf428b-91cf-491d-b843-da2b5da691db
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.63d10ee3-d285-4be2-a001-d1b852769639:
        id: 63d10ee3-d285-4be2-a001-d1b852769639
        name: stage_sg
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: ipsec-vpn
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ffb6b092-f455-43d1-a04f-bbda23cdf0d9
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 63d10ee3-d285-4be2-a001-d1b852769639
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 63d10ee3-d285-4be2-a001-d1b852769639
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.6604da16-2461-4199-a70b-adcfc7d7954e:
        id: 6604da16-2461-4199-a70b-adcfc7d7954e
        name: maxpatrol_sg
        description: You can select protocols and ports that the inbound rule will
            apply to. If you do not select any protocols and ports, no protocols and
            ports will be opened. After a security group is created, you can add or
            modify security group rules as required.
        rules:
            -   description: SEC-1168
                direction: ingress
                ethertype: IPv4
                protocol_port: 514
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: temporarily
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.670bf91b-0d2c-4857-acdf-9479d8e9c893:
        id: 670bf91b-0d2c-4857-acdf-9479d8e9c893
        name: vipnet-block-snat
        description: vipnet-test
        rules:
            -   description: block-udp
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: block-udp
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 670bf91b-0d2c-4857-acdf-9479d8e9c893
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 670bf91b-0d2c-4857-acdf-9479d8e9c893
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.695bb9fe-8341-45d7-bef4-76665c64f73b:
        id: 695bb9fe-8341-45d7-bef4-76665c64f73b
        name: multifactor_sg
        description: Inbound ICMP and other traffic on ports 22, 80, 443, and 3389
            are allowed. Such a security group is used for remote login, ping, and
            hosting websites on ECSs.
        rules:
            -   description: multifactor
                direction: ingress
                ethertype: IPv4
                protocol_port: 1812
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.69d5c1ce-ab49-4db7-8f24-e5a32e7fa9c7:
        id: 69d5c1ce-ab49-4db7-8f24-e5a32e7fa9c7
        name: release_dmz
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: '1'
                direction: ingress
                ethertype: IPv4
                protocol_port: 9090
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Grafana Prometheus
                direction: ingress
                ethertype: IPv4
                protocol_port: 8404
                protocol: tcp
                remote_group_id: 2fa01d3b-0cef-4467-a3fd-66037771405d
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: haproxy stats
                direction: ingress
                ethertype: IPv4
                protocol_port: 8888
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 69d5c1ce-ab49-4db7-8f24-e5a32e7fa9c7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 69d5c1ce-ab49-4db7-8f24-e5a32e7fa9c7
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.6cb836d6-e83f-4a1c-a72f-54969e08b23a:
        id: 6cb836d6-e83f-4a1c-a72f-54969e08b23a
        name: postgres-db
        description: Allowing traffic on all ports may introduce security risks. Exercise
            caution when selecting this option.
        rules:
            -   description: Allow all traffic
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 6cb836d6-e83f-4a1c-a72f-54969e08b23a
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 6cb836d6-e83f-4a1c-a72f-54969e08b23a
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.6cdb2645-f31d-42e7-b3ed-166076b90cfd:
        id: 6cdb2645-f31d-42e7-b3ed-166076b90cfd
        name: devel_troikaservice
        description: traffic to ts-gw1.tkp2.devel
        rules:
            -   description: sdbp.47
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 6cdb2645-f31d-42e7-b3ed-166076b90cfd
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 6cdb2645-f31d-42e7-b3ed-166076b90cfd
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.6f86479f-6611-48a2-9c48-5e48076a94bb:
        id: 6f86479f-6611-48a2-9c48-5e48076a94bb
        name: metabase
        description: metabase and airflow group
        rules:
            -   description: SUP-3344
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: airflow
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: 454b4e24-3d9a-4955-ab20-753ad053af86
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 27dc4465-32d9-4665-a5d7-a552906c2b10
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 6f86479f-6611-48a2-9c48-5e48076a94bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 6f86479f-6611-48a2-9c48-5e48076a94bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.6fb565d5-97bc-4d0f-83d0-55bbba8acde3:
        id: 6fb565d5-97bc-4d0f-83d0-55bbba8acde3
        name: replicadb-sg
        description: You can select protocols and ports that the inbound rule will
            apply to. If you do not select any protocols and ports, no protocols and
            ports will be opened. After a security group is created, you can add or
            modify security group rules as required.
        rules:
            -   description: 64serviceM
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: Allows the instances in the security group to communicate
                    with each other over a private network.
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 6fb565d5-97bc-4d0f-83d0-55bbba8acde3
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allows all traffic from the instances in the security
                    group to external resources.
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allows all traffic from the instances in the security
                    group to external resources.
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allows the instances in the security group to communicate
                    with each other over a private network.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 6fb565d5-97bc-4d0f-83d0-55bbba8acde3
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.7535b86e-7a7d-4e95-b181-389f30b0d121:
        id: 7535b86e-7a7d-4e95-b181-389f30b0d121
        name: carantine
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 7535b86e-7a7d-4e95-b181-389f30b0d121
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 7535b86e-7a7d-4e95-b181-389f30b0d121
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.79f06ee9-944d-48ea-9613-aac5d9b8a8a5:
        id: 79f06ee9-944d-48ea-9613-aac5d9b8a8a5
        name: megafon
        description: You can select protocols and ports that the inbound rule will
            apply to. If you do not select any protocols and ports, no protocols and
            ports will be opened. After a security group is created, you can add or
            modify security group rules as required.
        rules:
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 79f06ee9-944d-48ea-9613-aac5d9b8a8a5
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: null
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: null
                direction: ingress
                ethertype: IPv4
                protocol_port: 3389
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: null
                direction: ingress
                ethertype: IPv4
                protocol_port: 23
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 79f06ee9-944d-48ea-9613-aac5d9b8a8a5
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.7dffc36d-af6c-45dc-a7d8-02f26440b6f6:
        id: 7dffc36d-af6c-45dc-a7d8-02f26440b6f6
        name: cce-new-ticket-system-dev-cce-control-civh1
        description: Do not delete or update this security group which is created
            by CCE cluster 59e40b3b-abf7-11ef-8c9a-0255ac10008b for the master port
        rules:
            -   description: Created by CCE,please don't modify! Used by the networking
                    add-on on the worker node to access the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: 9443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by worker nodes
                    to access each other and to access the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Kube-apiserver service
                    port, used to manage the lifecycle of K8s resources.
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by the storage
                    add-on on the worker node to access the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: 8445
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Listening port of
                    kube-apiserver of the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: 5443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Traffic from the
                    source IP addresses defined in the security group must be allowed.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 7dffc36d-af6c-45dc-a7d8-02f26440b6f6
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Kube-apiserver service
                    port, used to manage the lifecycle of K8s resources.
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: 'Created by CCE,please don''t modify! Default egress
                    security group rule '
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.8031c07d-5996-4cb9-8d80-2c2d9dfa28f4:
        id: 8031c07d-5996-4cb9-8d80-2c2d9dfa28f4
        name: sftp-operating54-sg
        description: You can select protocols and ports that the inbound rule will
            apply to. If you do not select any protocols and ports, no protocols and
            ports will be opened. After a security group is created, you can add or
            modify security group rules as required.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 30901
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 30901
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 8031c07d-5996-4cb9-8d80-2c2d9dfa28f4
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 8031c07d-5996-4cb9-8d80-2c2d9dfa28f4
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.85756d29-2c15-4b33-b69c-9bb4548b4782:
        id: 85756d29-2c15-4b33-b69c-9bb4548b4782
        name: Migrate-SystemProject
        description: ''
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8900
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8899
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 85756d29-2c15-4b33-b69c-9bb4548b4782
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 85756d29-2c15-4b33-b69c-9bb4548b4782
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.878443a9-2ba4-470c-8f34-2d99c783a719:
        id: 878443a9-2ba4-470c-8f34-2d99c783a719
        name: corp_jira_sg
        description: The security group is for jira and confluence
        rules:
            -   description: INC-251116
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: INC-251112
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SEC-831
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SEC-831
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***************/32
                remote_address_group_id: null
            -   description: SEC-831
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: SUP-1776
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: https://job.sbertroika.ru/browse/SEC-889
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: WAF
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: WAF
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 3c8d655b-1343-41e5-b8f0-4dbe86d44f36
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: WAF
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 3c8d655b-1343-41e5-b8f0-4dbe86d44f36
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 878443a9-2ba4-470c-8f34-2d99c783a719
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Loki
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow access to DB KSC for collect logs
                direction: egress
                ethertype: IPv4
                protocol_port: 1433
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: "\u0414\u043E\u0441\u0442\u0443\u043F\u043D\u043E\u0441\
                    \u0442\u044C \u0434\u043E \u0431\u0440\u043E\u043A\u0435\u0440\
                    \u0430"
                direction: ingress
                ethertype: IPv4
                protocol_port: 514
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 459bc5ea-7f37-4d57-b8e6-8bf1a397ee4b
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: 88 ipa
                direction: ingress
                ethertype: IPv4
                protocol_port: 88
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: zabbix.st.corp
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTPS
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTP
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.90d8f173-fb6e-4807-b866-bf4c3a6481ba:
        id: 90d8f173-fb6e-4807-b866-bf4c3a6481ba
        name: st_consultant
        description: "\u0418\u0437\u043E\u043B\u0438\u0440\u043E\u0432\u0430\u043D\
            \u043D\u0430\u044F \u0433\u0440\u0443\u043F\u043F\u0430"
        rules:
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 90d8f173-fb6e-4807-b866-bf4c3a6481ba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 90d8f173-fb6e-4807-b866-bf4c3a6481ba
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.9324a9dd-7ae1-4f1e-9791-53226c6c3cba:
        id: 9324a9dd-7ae1-4f1e-9791-53226c6c3cba
        name: t2_db
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: SUP-2615
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: PMM
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ae6c4410-6915-41a8-8a08-b3b9b17af481
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ping from airflow
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Airflow
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 54acd510-91b5-405c-81f0-3ed0365feb8c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: PMA
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 9324a9dd-7ae1-4f1e-9791-53226c6c3cba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9324a9dd-7ae1-4f1e-9791-53226c6c3cba
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.98ea1828-b41e-45be-af01-59fa4306cf01:
        id: 98ea1828-b41e-45be-af01-59fa4306cf01
        name: nexus_sg
        description: You can select protocols and ports that the inbound rule will
            apply to. If you do not select any protocols and ports, no protocols and
            ports will be opened. After a security group is created, you can add or
            modify security group rules as required.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8081
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 98ea1828-b41e-45be-af01-59fa4306cf01
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 98ea1828-b41e-45be-af01-59fa4306cf01
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.9960a984-0c32-4672-9ddd-bbf606cb31d0:
        id: 9960a984-0c32-4672-9ddd-bbf606cb31d0
        name: AD_server_sg
        description: Active directory
        rules:
            -   description: Allow all traffic
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 9960a984-0c32-4672-9ddd-bbf606cb31d0
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9960a984-0c32-4672-9ddd-bbf606cb31d0
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.99bfb8e9-0b41-4292-8759-be0d65dac14c:
        id: 99bfb8e9-0b41-4292-8759-be0d65dac14c
        name: dev_web_sg
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 250392d6-d6f3-4fe1-a22c-bbee50f8129c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 99bfb8e9-0b41-4292-8759-be0d65dac14c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 250392d6-d6f3-4fe1-a22c-bbee50f8129c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: "\u0418\u043D\u0442\u0435\u0440\u0444\u0435\u0439\u0441\
                    \ \u0442\u0435\u0440\u043C\u0438\u043D\u0430\u043B\u043E\u0432\
                    \ \u0438 \u0432\u043D\u0435\u0448\u043D\u0438\u0445 \u0423\u0420\
                    \u041B"
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 99bfb8e9-0b41-4292-8759-be0d65dac14c
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.9e11583f-fa1b-42b6-8ad8-127b2f096875:
        id: 9e11583f-fa1b-42b6-8ad8-127b2f096875
        name: stage_web_server
        description: The security group is for general-purpose web servers and includes
            default rules that allow all inbound ICMP traffic and inbound traffic
            on ports 22, 80, 443, and 3389. The security group is used for remote
            login, ping, and hosting a website on ECSs.
        rules:
            -   description: nspk
                direction: ingress
                ethertype: IPv4
                protocol_port: 4000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTPS
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to connect Qurator
                direction: ingress
                ethertype: IPv4
                protocol_port: 8443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9e11583f-fa1b-42b6-8ad8-127b2f096875
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 9e11583f-fa1b-42b6-8ad8-127b2f096875
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to access websites over HTTP
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.9f5644be-cb0b-44ed-b062-c8fcb3d4d8e6:
        id: 9f5644be-cb0b-44ed-b062-c8fcb3d4d8e6
        name: cce-new-ticket-system-dev-cce-node-civh1
        description: The security group is created by CCE cluster 59e40b3b-abf7-11ef-8c9a-0255ac10008b
            for the node
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: 59bd8e08-c7f3-4f8e-ae5b-b539996f83b8
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by the master
                    node to proactively access kubelet of the node (for example, by
                    running kubectl exec {pod}).
                direction: ingress
                ethertype: IPv4
                protocol_port: 10250
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Default access port
                    range of the NodePort service in the cluster.
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: 'Created by CCE,please don''t modify! Default egress
                    security group rule '
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by worker nodes
                    to access each other and to access the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Traffic from the
                    source IP addresses defined in the security group must be allowed.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9f5644be-cb0b-44ed-b062-c8fcb3d4d8e6
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Default access port
                    range of the NodePort service in the cluster.
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Allows remote access
                    to a Linux ECS using SSH.
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.9f822429-bd32-4b0c-a677-b2b3fa3b9115:
        id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
        name: corp_vpn_sg
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 1e0517ba-ee8e-4bc1-adbd-6c0c5b4f047c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SUP-3435
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: b4e64cc8-aff5-4ed2-aaf9-cfdbe5498864
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: DC-2636
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: cfcee57b-d2d9-4faf-8193-ab9d12bc87d0
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 41bc0b0b-a9c6-46b4-8f1d-1e0490e46aae
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 69d5c1ce-ab49-4db7-8f24-e5a32e7fa9c7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ovpn
                direction: ingress
                ethertype: IPv4
                protocol_port: 1194
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.a0ca7a69-eb41-46b7-9b38-ba09b964a810:
        id: a0ca7a69-eb41-46b7-9b38-ba09b964a810
        name: bizone_soc
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: "\u0434\u043E\u0441\u0442\u0443\u043F\u043D\u043E\u0441\
                    \u0442\u044C  DLP"
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: logbroker
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 41bc0b0b-a9c6-46b4-8f1d-1e0490e46aae
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SEC-1168
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: logbroker
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 514
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: gitlab.st.corp
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: logbroker
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: a0ca7a69-eb41-46b7-9b38-ba09b964a810
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: a0ca7a69-eb41-46b7-9b38-ba09b964a810
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.a22748e2-0868-46ce-b2aa-d24697b7f6b4:
        id: a22748e2-0868-46ce-b2aa-d24697b7f6b4
        name: cce-corp-new-cce-node-e1yrk
        description: The security group is created by CCE cluster f9df0e91-c55e-11ed-a536-0255ac100048
            for the node
        rules:
            -   description: Promtail agent
                direction: ingress
                ethertype: IPv4
                protocol_port: 31427
                protocol: tcp
                remote_group_id: a8fb077a-b737-46af-9c76-e6e0d90f86d3
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Promtail agent
                direction: ingress
                ethertype: IPv4
                protocol_port: 31427
                protocol: tcp
                remote_group_id: 878443a9-2ba4-470c-8f34-2d99c783a719
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: 134f109d-544d-4c53-bdf3-5350b627ab7c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10250
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10250
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 4789
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10250
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: a22748e2-0868-46ce-b2aa-d24697b7f6b4
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.a382bf28-f2d4-4526-85a9-5af4ad85517b:
        id: a382bf28-f2d4-4526-85a9-5af4ad85517b
        name: 64saratov
        description: Inbound ICMP and other traffic on ports 22, 80, 443, and 3389
            are allowed. Such a security group is used for remote login, ping, and
            hosting websites on ECSs.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: a382bf28-f2d4-4526-85a9-5af4ad85517b
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: a382bf28-f2d4-4526-85a9-5af4ad85517b
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.a4ed22b1-f4ba-401e-931e-5cb728500d5c:
        id: a4ed22b1-f4ba-401e-931e-5cb728500d5c
        name: default
        description: default
        rules:
            -   description: sber radar
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: sber radar
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 8443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: '-'
                direction: egress
                ethertype: IPv4
                protocol_port: 8080
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTP
                direction: egress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTPS
                direction: egress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: egress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to upload and download files
                direction: egress
                ethertype: IPv4
                protocol_port: 21 -  20
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to ECSs
                direction: egress
                ethertype: IPv4
                protocol_port: 23
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Windows ECSs
                direction: egress
                ethertype: IPv4
                protocol_port: 3389
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to upload and download files
                direction: ingress
                ethertype: IPv4
                protocol_port: 21 -  20
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTPS
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 23
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTP
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Windows ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 3389
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: '-'
                direction: ingress
                ethertype: IPv4
                protocol_port: 8080
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: null
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: a4ed22b1-f4ba-401e-931e-5cb728500d5c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: a4ed22b1-f4ba-401e-931e-5cb728500d5c
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.a8fb077a-b737-46af-9c76-e6e0d90f86d3:
        id: a8fb077a-b737-46af-9c76-e6e0d90f86d3
        name: corp_crowd_sg
        description: The security group is for general-purpose web servers and includes
            default rules that allow all inbound ICMP traffic and inbound traffic
            on ports 22, 80, 443, and 3389. The security group is used for remote
            login, ping, and hosting a website on ECSs.
        rules:
            -   description: TEST CORP VPN
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ed87592e-e7f9-436a-97a0-d886579b72f6
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Loki
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: a8fb077a-b737-46af-9c76-e6e0d90f86d3
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTPS
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: a8fb077a-b737-46af-9c76-e6e0d90f86d3
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTP
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.ab1088a2-2c72-47fc-a1fb-b9dc2fefa8f7:
        id: ab1088a2-2c72-47fc-a1fb-b9dc2fefa8f7
        name: mysql-backup
        description: database backup services here
        rules:
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: 250392d6-d6f3-4fe1-a22c-bbee50f8129c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: 1822be21-c581-4211-937d-50ededac31c1
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 3389
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: ab1088a2-2c72-47fc-a1fb-b9dc2fefa8f7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ab1088a2-2c72-47fc-a1fb-b9dc2fefa8f7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.ae6c4410-6915-41a8-8a08-b3b9b17af481:
        id: ae6c4410-6915-41a8-8a08-b3b9b17af481
        name: pmm
        description: Percona Management and Monitoring
        rules:
            -   description: "\u0411\u0414 2 \u043A\u043E\u043D\u0442\u0443\u0440\u0430"
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/24
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 63d10ee3-d285-4be2-a001-d1b852769639
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 250392d6-d6f3-4fe1-a22c-bbee50f8129c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1822be21-c581-4211-937d-50ededac31c1
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9324a9dd-7ae1-4f1e-9791-53226c6c3cba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: ae6c4410-6915-41a8-8a08-b3b9b17af481
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ae6c4410-6915-41a8-8a08-b3b9b17af481
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.b4e64cc8-aff5-4ed2-aaf9-cfdbe5498864:
        id: b4e64cc8-aff5-4ed2-aaf9-cfdbe5498864
        name: cce-new-ticket-system-sg
        description: You can select protocols and ports that the inbound rule will
            apply to. If you do not select any protocols and ports, no protocols and
            ports will be opened. After a security group is created, you can add or
            modify security group rules as required.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: 2cadc7b2-f244-46de-9d9a-2f5cbab7a342
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: b4e64cc8-aff5-4ed2-aaf9-cfdbe5498864
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SUP-3435
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: b4e64cc8-aff5-4ed2-aaf9-cfdbe5498864
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: b4e64cc8-aff5-4ed2-aaf9-cfdbe5498864
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.b61d46ee-7b37-4e5a-a691-2ab64ac1a6cf:
        id: b61d46ee-7b37-4e5a-a691-2ab64ac1a6cf
        name: kasperskiy-gw
        description: "kasperskiy-gw \u0441\u0432\u044F\u0437\u044C \u0441 srv"
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 15000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 15000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 15000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 13000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 13000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 13000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 15000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 13000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8080
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: 8080
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: b61d46ee-7b37-4e5a-a691-2ab64ac1a6cf
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: b61d46ee-7b37-4e5a-a691-2ab64ac1a6cf
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: TEST CONN ALL
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: NTP
                direction: egress
                ethertype: IPv4
                protocol_port: 123
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_4_TCP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_1_UDP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_2_TCP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_2_UDP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_1_TCP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: DNS_4_TCP
                direction: egress
                ethertype: IPv4
                protocol_port: 53
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: internet
                direction: egress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: internet
                direction: egress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.bdb89e4f-8e53-4a92-8743-ab9f0d2eebdc:
        id: bdb89e4f-8e53-4a92-8743-ab9f0d2eebdc
        name: sc_mail
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: bacula fd
                direction: ingress
                ethertype: IPv4
                protocol_port: 9102
                protocol: tcp
                remote_group_id: ee19366c-760e-49e7-adf8-9b3c30a6fd65
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: pop
                direction: ingress
                ethertype: IPv4
                protocol_port: 995
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: zabbix vdc1
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 27dc4465-32d9-4665-a5d7-a552906c2b10
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: smtp
                direction: ingress
                ethertype: IPv4
                protocol_port: 465
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: imap
                direction: ingress
                ethertype: IPv4
                protocol_port: 993
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: https
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: bdb89e4f-8e53-4a92-8743-ab9f0d2eebdc
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: bdb89e4f-8e53-4a92-8743-ab9f0d2eebdc
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.c1980429-eccc-40cb-b6f3-08f3a2420472:
        id: c1980429-eccc-40cb-b6f3-08f3a2420472
        name: vpnrestrict_sg
        description: Inbound ICMP and other traffic on ports 22, 80, 443, and 3389
            are allowed. Such a security group is used for remote login, ping, and
            hosting websites on ECSs.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 1194
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: c1980429-eccc-40cb-b6f3-08f3a2420472
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: c1980429-eccc-40cb-b6f3-08f3a2420472
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.c3995505-d85a-4c89-ac8a-f2c1b31faace:
        id: c3995505-d85a-4c89-ac8a-f2c1b31faace
        name: cce-develop-cce-node-uerr5
        description: The security group is created by CCE cluster 72b5b69e-04ce-11f0-ba84-0255ac10008f
            for the node
        rules:
            -   description: Created by CCE,please don't modify! Traffic from the
                    source IP addresses defined in the security group must be allowed.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: c3995505-d85a-4c89-ac8a-f2c1b31faace
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by containers
                    to access each other.
                direction: ingress
                ethertype: IPv4
                protocol_port: 4789
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: 'Created by CCE,please don''t modify! Default egress
                    security group rule '
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Default access port
                    range of the NodePort service in the cluster.
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by the master
                    node to proactively access kubelet of the node (for example, by
                    running kubectl exec {pod}).
                direction: ingress
                ethertype: IPv4
                protocol_port: 10250
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Default access port
                    range of the NodePort service in the cluster.
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Allows remote access
                    to a Linux ECS using SSH.
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.c6947f41-90a6-4902-bc16-20b33fa97a57:
        id: c6947f41-90a6-4902-bc16-20b33fa97a57
        name: vipnet
        description: Allowing traffic on all ports may introduce security risks. Exercise
            caution when selecting this option.
        rules:
            -   description: saratov
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: megafon
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ********/8
                remote_address_group_id: null
            -   description: megafon
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: saratov
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/24
                remote_address_group_id: null
            -   description: sftp
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: sftp
                direction: ingress
                ethertype: IPv4
                protocol_port: 2223
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: VIPENTERPRISE
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: vipnet
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: vipnet coordinator
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: SEC-979
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 9090
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8090
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: c6947f41-90a6-4902-bc16-20b33fa97a57
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.cc9da9e2-8185-440a-b7b5-31451e81fcb5:
        id: cc9da9e2-8185-440a-b7b5-31451e81fcb5
        name: sc_mail2
        description: The security group is for general-purpose web servers. It allows
            inbound ICMP and other traffic on ports 22, 80, 443, and 3389. This group
            is used for remote login, ping, and hosting websites on ECSs.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: cc9da9e2-8185-440a-b7b5-31451e81fcb5
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: zabbix vdc1
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: bacula fd
                direction: ingress
                ethertype: IPv4
                protocol_port: 9102
                protocol: tcp
                remote_group_id: ee19366c-760e-49e7-adf8-9b3c30a6fd65
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: imap
                direction: ingress
                ethertype: IPv4
                protocol_port: 993
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: pop
                direction: ingress
                ethertype: IPv4
                protocol_port: 995
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: https
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 27dc4465-32d9-4665-a5d7-a552906c2b10
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: bdb89e4f-8e53-4a92-8743-ab9f0d2eebdc
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: bdb89e4f-8e53-4a92-8743-ab9f0d2eebdc
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: smtp
                direction: ingress
                ethertype: IPv4
                protocol_port: 465
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.ce00ccc9-83b9-48cf-8772-39be4b567b16:
        id: ce00ccc9-83b9-48cf-8772-39be4b567b16
        name: etl
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ce00ccc9-83b9-48cf-8772-39be4b567b16
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: ce00ccc9-83b9-48cf-8772-39be4b567b16
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.ce572141-1b08-4d2e-9bae-c58fe6ee0ad2:
        id: ce572141-1b08-4d2e-9bae-c58fe6ee0ad2
        name: sg_soc
        description: https://job.sbertroika.ru/browse/SEC-838
        rules:
            -   description: https://job.sbertroika.ru/browse/SEC-838
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Create by sfs turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 445
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: SEC-831
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SEC-831
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: SEC-831
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: SUP-1776
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *************/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 41bc0b0b-a9c6-46b4-8f1d-1e0490e46aae
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SUP-1788
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***************/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ************/18
                remote_address_group_id: null
            -   description: 1C
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 389
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ************/18
                remote_address_group_id: null
            -   description: Allow access to DB KSC for collect logs
                direction: egress
                ethertype: IPv4
                protocol_port: 1433
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: Used to access PostgreSQL databases
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: dd098b0b-62de-4cec-b3b9-6fafd8b9beb4
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: zabbix.st.corp
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2051
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 20048
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5432
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/8
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 459bc5ea-7f37-4d57-b8e6-8bf1a397ee4b
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2049
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: "\u0414\u043E\u0441\u0442\u0443\u043F\u043D\u043E\u0441\
                    \u0442\u044C \u0434\u043E \u0431\u0440\u043E\u043A\u0435\u0440\
                    \u0430"
                direction: ingress
                ethertype: IPv4
                protocol_port: 514
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Preview tkp2 db
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *********/32
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 20048
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 111
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2052
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Percona PMM
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: 1822be21-c581-4211-937d-50ededac31c1
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: 389 ipa
                direction: ingress
                ethertype: IPv4
                protocol_port: 389
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 111
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: 88 ipa
                direction: ingress
                ethertype: IPv4
                protocol_port: 88
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.cfcee57b-d2d9-4faf-8193-ab9d12bc87d0:
        id: cfcee57b-d2d9-4faf-8193-ab9d12bc87d0
        name: 1c_sg
        description: "\u0414\u043B\u044F \u0441\u0435\u0440\u0432\u0435\u0440\u0430\
            \ 1\u0441 "
        rules:
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: sedo.fss.ru
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: cfcee57b-d2d9-4faf-8193-ab9d12bc87d0
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: cfcee57b-d2d9-4faf-8193-ab9d12bc87d0
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.d4447139-e22b-40c9-bc88-acc22dbc87bb:
        id: d4447139-e22b-40c9-bc88-acc22dbc87bb
        name: Test-IPA-AD
        description: The security group is for general-purpose web servers and includes
            default rules that allow all inbound ICMP traffic and inbound traffic
            on ports 22, 80, 443, and 3389. The security group is used for remote
            login, ping, and hosting a website on ECSs.
        rules:
            -   description: from usergate
                direction: ingress
                ethertype: IPv4
                protocol_port: 389
                protocol: tcp
                remote_group_id: 41bc0b0b-a9c6-46b4-8f1d-1e0490e46aae
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: VRRP
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ***********/32
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTPS
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTP
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: d4447139-e22b-40c9-bc88-acc22dbc87bb
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Windows ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 3389
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: d4447139-e22b-40c9-bc88-acc22dbc87bb
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.d545983f-86c6-42aa-b996-6c449cc5762d:
        id: d545983f-86c6-42aa-b996-6c449cc5762d
        name: cce-new-ticket-system-dev-cce-eni-civh1
        description: The security group is created by CCE yangtse cluster 59e40b3b-abf7-11ef-8c9a-0255ac10008b
            for the eni or subeni
        rules:
            -   description: 'Created by CCE,please don''t modify! Default egress
                    security group rule '
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Used by worker nodes
                    to access each other and to access the master node.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: *********/24
                remote_address_group_id: null
            -   description: Created by CCE,please don't modify! Traffic from the
                    source IP addresses defined in the security group must be allowed.
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: d545983f-86c6-42aa-b996-6c449cc5762d
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.d69210d7-0758-4d9a-9d8d-cc701d0fa45d:
        id: d69210d7-0758-4d9a-9d8d-cc701d0fa45d
        name: kasperskiy-all-open
        description: Allowing traffic on all ports may introduce security risks. Exercise
            caution when selecting this option.
        rules:
            -   description: Allow all traffic
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: d69210d7-0758-4d9a-9d8d-cc701d0fa45d
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: d69210d7-0758-4d9a-9d8d-cc701d0fa45d
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.d902aeaa-1c75-4a74-9724-68868b4f15d7:
        id: d902aeaa-1c75-4a74-9724-68868b4f15d7
        name: sdbp_and_nsi
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: d902aeaa-1c75-4a74-9724-68868b4f15d7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: d902aeaa-1c75-4a74-9724-68868b4f15d7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.dd098b0b-62de-4cec-b3b9-6fafd8b9beb4:
        id: dd098b0b-62de-4cec-b3b9-6fafd8b9beb4
        name: cce-corp-cluster-sg
        description: The security group is created by CCE cluster e920c23c-2074-11ed-9451-0255ac10004e
            for the node
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ************/18
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 32767 -  30000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: dd098b0b-62de-4cec-b3b9-6fafd8b9beb4
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.de1a9be0-8830-49f9-93e7-2e99842f8598:
        id: de1a9be0-8830-49f9-93e7-2e99842f8598
        name: ubuntuRiver
        description: Website 80, 443 22
        rules:
            -   description: RIVERUBUNTU
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 1822be21-c581-4211-937d-50ededac31c1
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to access websites over HTTP
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: de1a9be0-8830-49f9-93e7-2e99842f8598
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 27dc4465-32d9-4665-a5d7-a552906c2b10
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to access websites over HTTPS
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: de1a9be0-8830-49f9-93e7-2e99842f8598
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.de4874a3-749a-4b67-b4ed-834159f36c3e:
        id: de4874a3-749a-4b67-b4ed-834159f36c3e
        name: test_sg
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: '1111'
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 037d16bd-2d5c-462a-bd5c-93c22a1d6362
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: TEMP
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Prometheus
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/8
                remote_address_group_id: null
            -   description: KASPER
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: SUP-2350
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 0d4b9cd4-b8cd-46f8-847c-6d65251a8687
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 2fa01d3b-0cef-4467-a3fd-66037771405d
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: dev-cluster
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 2fa01d3b-0cef-4467-a3fd-66037771405d
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 3306
                protocol: tcp
                remote_group_id: ab1088a2-2c72-47fc-a1fb-b9dc2fefa8f7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Zabbix ADV
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 250392d6-d6f3-4fe1-a22c-bbee50f8129c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 6379
                protocol: tcp
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: percona pmm
                direction: ingress
                ethertype: IPv4
                protocol_port: 51999 -  42000
                protocol: tcp
                remote_group_id: 1f449028-889b-4a82-9c12-96f4a9a6eaba
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: tcp
                remote_group_id: 135dab5f-aef8-4ebc-8897-cd10abd3cc9b
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: de4874a3-749a-4b67-b4ed-834159f36c3e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.e39386bf-ffd2-48d2-a8bc-428aede131a7:
        id: e39386bf-ffd2-48d2-a8bc-428aede131a7
        name: ftp
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: vpn
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/32
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 27dc4465-32d9-4665-a5d7-a552906c2b10
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 6100 -  6000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 21 -  20
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: e39386bf-ffd2-48d2-a8bc-428aede131a7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: e39386bf-ffd2-48d2-a8bc-428aede131a7
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.e9523972-1305-4807-80b7-9798a29c3f5c:
        id: e9523972-1305-4807-80b7-9798a29c3f5c
        name: spaceJS
        description: "\u0413\u0440\u0443\u043F\u043F\u0430 \u0434\u043B\u044F \u0441\
            \u0432\u044F\u0437\u0438 space jump (\u0438\u0437 dmz) \u0441 \u0441\u0435\
            \u0440\u0432\u0435\u0440\u043E\u043C (\u0432 corp)."
        rules:
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: e9523972-1305-4807-80b7-9798a29c3f5c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: e9523972-1305-4807-80b7-9798a29c3f5c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.ebb4f001-778b-4526-a6b3-c0106ab9e709:
        id: ebb4f001-778b-4526-a6b3-c0106ab9e709
        name: cce-dev-cce-control-nhnno
        description: Do not delete or update this security group which is created
            by CCE cluster 200792b2-a62c-11ed-a536-0255ac100048 for the master port
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 8445
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 9443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: **********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5444
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: ********/16
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 4789
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ebb4f001-778b-4526-a6b3-c0106ab9e709
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 5443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: null
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.ec9cae70-5a83-42c3-b26e-0c7f3daf9767:
        id: ec9cae70-5a83-42c3-b26e-0c7f3daf9767
        name: release_ftp
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: LEONOVICH
                direction: ingress
                ethertype: IPv4
                protocol_port: 43097
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: 05.02.2025
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 41bc0b0b-a9c6-46b4-8f1d-1e0490e46aae
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: 05.02.2025
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 21
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ec9cae70-5a83-42c3-b26e-0c7f3daf9767
                remote_ip_prefix: null
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.ed87592e-e7f9-436a-97a0-d886579b72f6:
        id: ed87592e-e7f9-436a-97a0-d886579b72f6
        name: corp_vpn_test
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 1194
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 1194
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ed87592e-e7f9-436a-97a0-d886579b72f6
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: ed87592e-e7f9-436a-97a0-d886579b72f6
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.ee19366c-760e-49e7-adf8-9b3c30a6fd65:
        id: ee19366c-760e-49e7-adf8-9b3c30a6fd65
        name: backup
        description: backup tools in this group
        rules:
            -   description: zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: baculum web
                direction: ingress
                ethertype: IPv4
                protocol_port: 9095
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: baculum API interface
                direction: ingress
                ethertype: IPv4
                protocol_port: 9096
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Bacula dir port
                direction: ingress
                ethertype: IPv4
                protocol_port: 9103
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Bacula dir port
                direction: ingress
                ethertype: IPv4
                protocol_port: 9102
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 10000
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Bacula dir port
                direction: ingress
                ethertype: IPv4
                protocol_port: 9101
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ee19366c-760e-49e7-adf8-9b3c30a6fd65
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: ee19366c-760e-49e7-adf8-9b3c30a6fd65
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.ee656bfe-2bc1-4aa4-8088-e86e09563fa9:
        id: ee656bfe-2bc1-4aa4-8088-e86e09563fa9
        name: elastic
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: SUP-3440
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 9200
                protocol: tcp
                remote_group_id: ebb4f001-778b-4526-a6b3-c0106ab9e709
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: fluentd pods
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 2fa01d3b-0cef-4467-a3fd-66037771405d
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 111
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2051
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2049
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 111
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 2052
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Created for SFS Turbo
                direction: ingress
                ethertype: IPv4
                protocol_port: 20048
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 10.0.0.0/8
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ee656bfe-2bc1-4aa4-8088-e86e09563fa9
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: ee656bfe-2bc1-4aa4-8088-e86e09563fa9
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.fda4410a-8acc-4d19-9716-4a828c34fd38:
        id: fda4410a-8acc-4d19-9716-4a828c34fd38
        name: t2_dmz
        description: Inbound traffic is not allowed on any port. After the security
            group is created, you can add or modify security group rules as required.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 6604da16-2461-4199-a70b-adcfc7d7954e
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Zabbix
                direction: ingress
                ethertype: IPv4
                protocol_port: 10051 -  10050
                protocol: tcp
                remote_group_id: 2a6b3c80-84c2-4992-97eb-cc868aca5f82
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 27dc4465-32d9-4665-a5d7-a552906c2b10
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 24556d98-c1b0-4f3f-b1d7-6f0593349018
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 54acd510-91b5-405c-81f0-3ed0365feb8c
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 443
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 80
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: fda4410a-8acc-4d19-9716-4a828c34fd38
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: fda4410a-8acc-4d19-9716-4a828c34fd38
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.ff65fde2-b5f5-4227-80f0-d6e578eaf395:
        id: ff65fde2-b5f5-4227-80f0-d6e578eaf395
        name: kali_sg
        description: Inbound ICMP and other traffic on ports 22, 80, 443, and 3389
            are allowed. Such a security group is used for remote login, ping, and
            hosting websites on ECSs.
        rules:
            -   description: ''
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: 9f822429-bd32-4b0c-a677-b2b3fa3b9115
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ff65fde2-b5f5-4227-80f0-d6e578eaf395
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: ff65fde2-b5f5-4227-80f0-d6e578eaf395
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
    sbertroika.security_groups.ffb6b092-f455-43d1-a04f-bbda23cdf0d9:
        id: ffb6b092-f455-43d1-a04f-bbda23cdf0d9
        name: ipsec-vpn
        description: The security group is for general-purpose web servers. It allows
            inbound ICMP and other traffic on ports 22, 80, 443, and 3389. This group
            is used for remote login, ping, and hosting websites on ECSs.
        rules:
            -   description: stage_sg
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: 63d10ee3-d285-4be2-a001-d1b852769639
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: yandex ipsec gw
                direction: ingress
                ethertype: IPv4
                protocol_port: 4500
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: yandex ipsec gw
                direction: ingress
                ethertype: IPv4
                protocol_port: 500
                protocol: udp
                remote_group_id: null
                remote_ip_prefix: **************/32
                remote_address_group_id: null
            -   description: Used to test the ECS connectivity with the ping command
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: icmp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Used to remotely connect to Linux ECSs
                direction: ingress
                ethertype: IPv4
                protocol_port: 22
                protocol: tcp
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: 0.0.0.0/0
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv4
                protocol_port: All
                protocol: null
                remote_group_id: ffb6b092-f455-43d1-a04f-bbda23cdf0d9
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow ECSs in the same security group to communicate
                    with each other
                direction: ingress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: ffb6b092-f455-43d1-a04f-bbda23cdf0d9
                remote_ip_prefix: null
                remote_address_group_id: null
            -   description: Allow all traffic
                direction: egress
                ethertype: IPv6
                protocol_port: All
                protocol: null
                remote_group_id: null
                remote_ip_prefix: ::/0
                remote_address_group_id: null
        tenant: 0f1e00cf9d8025ec2f78c017d8b6a4a9
        DC: sbertroika.dc.adv
