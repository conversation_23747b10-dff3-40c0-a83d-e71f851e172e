#!/bin/bash

echo "Установка зависимостей для скрипта переноса CSV в Jira..."

# Проверяем наличие pip
if ! command -v pip &> /dev/null; then
    echo "Ошибка: pip не найден. Установите Python и pip."
    exit 1
fi

# Устанавливаем зависимости
echo "Устанавливаем зависимости..."
pip install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "Зависимости успешно установлены!"
    echo ""
    echo "Теперь вы можете запустить скрипт:"
    echo "python csv_to_jira.py ЕРО.csv --debug-row 2"
else
    echo "Ошибка при установке зависимостей!"
    exit 1
fi
