# Скрипт переноса задач из CSV в Jira

Этот скрипт предназначен для автоматического переноса задач из CSV файла в Jira систему.

## Установка зависимостей

```bash
pip install -r requirements.txt
```

## Использование

### Основной режим (обработка всего файла)

```bash
python csv_to_jira.py ЕРО.csv
```

### Режим отладки (обработка одной строки)

```bash
python csv_to_jira.py ЕРО.csv --debug-row 2
```

### Дополнительные параметры

```bash
python csv_to_jira.py ЕРО.csv \
    --jira-url https://job.sbertroika.ru \
    --username <EMAIL> \
    --debug-row 5
```

## Параметры

- `csv_file` - путь к CSV файлу (обязательный)
- `--debug-row N` - номер строки для отладки (начиная с 1)
- `--jira-url` - URL Jira сервера (по умолчанию: https://job.sbertroika.ru)
- `--username` - имя пользователя Jira (по умолчанию: <EMAIL>)

## Что делает скрипт

1. **Читает CSV файл** с разделителем "," и текстовыми полями в кавычках
2. **Создает задачи** типа Story в проекте OPDVST
3. **Заполняет поля задач**:
   - **Заголовок**: значение поля "Выявленное отклонение" (очищенное от переводов строк)
   - **Описание**: все поля в формате "Поле: значение"
   - **Теги**: значения полей "Вид исследуемого объекта 1/2/3 уровень" + обязательный тег "ЕРО"
   - **Компонент**: значение поля "Объект исследования"
4. **Автоматически создает** недостающие теги и компоненты

## Структура CSV файла

Ожидаемые поля в CSV:
- "Выявленное отклонение" - используется как заголовок задачи (автоматически очищается от переводов строк)
- "Вид исследуемого объекта 1 уровень" - тег
- "Вид исследуемого объекта 2 уровень" - тег
- "Вид исследуемого объекта 3 уровень" - тег
- "Объект исследования" - компонент
- Все остальные поля включаются в описание задачи

**Примечание**: Ко всем задачам автоматически добавляется тег "ЕРО".

## Безопасность

- Пароль запрашивается интерактивно и не сохраняется
- Логирование всех операций для отслеживания процесса
- Возможность отладки на отдельных строках перед полным запуском

## Примеры использования

### Отладка первой строки данных
```bash
python csv_to_jira.py ЕРО.csv --debug-row 2
```

### Обработка всего файла
```bash
python csv_to_jira.py ЕРО.csv
```

## Логирование

Скрипт выводит подробную информацию о процессе:
- Подключение к Jira
- Создание компонентов
- Создание задач
- Статистику по завершению

## Обработка ошибок

- Пропуск строк без заголовка задачи
- Логирование ошибок с указанием номера строки
- Продолжение работы при ошибках в отдельных строках
- Итоговая статистика успешных и неуспешных операций
